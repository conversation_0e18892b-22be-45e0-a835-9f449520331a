rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user is approved
    function isApproved() {
      return isAuthenticated() && 
             request.auth.token.status == 'approved';
    }
    
    // Helper function to check if user is admin
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.token.role == 'admin' && 
             request.auth.token.status == 'approved';
    }
    
    // Helper function to check if user has specific role
    function hasRole(role) {
      return isApproved() && request.auth.token.role == role;
    }
    
    // Helper function to check if user owns the document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Pending Registrations Collection
    // Only admins can read all pending registrations
    // Users can only read their own pending registration
    // System can create pending registrations during signup
    match /pendingRegistrations/{userId} {
      // Admins can read all pending registrations
      allow read: if isAdmin();
      
      // Users can read their own pending registration
      allow read: if isOwner(userId);
      
      // Allow creation during registration process (before user has claims)
      allow create: if isAuthenticated() && 
                       isOwner(userId) && 
                       request.resource.data.uid == userId &&
                       request.resource.data.status == 'pending';
      
      // Only admins can update (approve/reject)
      allow update: if isAdmin() && 
                       // Ensure only status and approval fields can be updated
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['status', 'approvedBy', 'approvedAt', 'rejectedBy', 'rejectedAt', 'rejectionReason']);
      
      // No one can delete pending registrations (for audit trail)
      allow delete: if false;
    }

    // Students Collection
    // Only approved users can access student data
    // Students can read their own data
    // Trainers and college staff can read student data
    // Admins have full access
    match /students/{studentId} {
      allow read: if hasRole('student') && isOwner(studentId) ||
                     hasRole('trainer') ||
                     hasRole('college-staff') ||
                     isAdmin();
      
      allow write: if hasRole('trainer') ||
                      hasRole('college-staff') ||
                      isAdmin();
    }

    // Trainers Collection
    // Trainers can read their own data
    // College staff and admins can read trainer data
    match /trainers/{trainerId} {
      allow read: if hasRole('trainer') && isOwner(trainerId) ||
                     hasRole('college-staff') ||
                     isAdmin();
      
      allow write: if hasRole('college-staff') ||
                      isAdmin();
    }

    // Parents Collection
    // Parents can read their own data
    // College staff and admins can read parent data
    match /parents/{parentId} {
      allow read: if hasRole('parent') && isOwner(parentId) ||
                     hasRole('college-staff') ||
                     isAdmin();
      
      allow write: if hasRole('college-staff') ||
                      isAdmin();
    }

    // Employees Collection (College Staff)
    // College staff can read their own data
    // Admins have full access
    match /employees/{employeeId} {
      allow read: if hasRole('college-staff') && isOwner(employeeId) ||
                     isAdmin();
      
      allow write: if isAdmin();
    }

    // Institutions Collection
    // All approved users can read institution data
    // Only college staff and admins can write
    match /institutions/{institutionId} {
      allow read: if isApproved();
      
      allow write: if hasRole('college-staff') ||
                      isAdmin();
    }

    // User Profiles Collection (for additional user data)
    // Users can read their own profile
    // Admins can read all profiles
    match /userProfiles/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      
      allow write: if isOwner(userId) || isAdmin();
    }

    // Audit Logs Collection (for tracking admin actions)
    // Only admins can read audit logs
    // System functions can write audit logs
    match /auditLogs/{logId} {
      allow read: if isAdmin();
      
      // Only allow creation of audit logs, no updates or deletes
      allow create: if isAdmin();
      
      allow update, delete: if false;
    }

    // Approved Domains Collection (for email domain verification)
    // All users can read approved domains for validation
    // Only admins can manage approved domains
    match /approvedDomains/{domainId} {
      allow read: if isAuthenticated();
      
      allow write: if isAdmin();
    }

    // System Configuration Collection
    // Only admins can access system configuration
    match /systemConfig/{configId} {
      allow read, write: if isAdmin();
    }

    // Notifications Collection
    // Users can read their own notifications
    // System can create notifications
    match /notifications/{userId} {
      allow read: if isOwner(userId);
      
      // Allow system to create notifications (via Cloud Functions)
      allow create: if isAuthenticated();
      
      // Users can mark notifications as read
      allow update: if isOwner(userId) && 
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['read', 'readAt']);
      
      allow delete: if isOwner(userId);
    }

    // Default deny rule for any other collections
    // This ensures security by default for any new collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

/*
Security Rules Summary:

1. **Pending Registrations**: 
   - Admins can read all, users can read their own
   - Users can create during signup, only admins can approve/reject
   - No deletions allowed (audit trail)

2. **Role-based Access**: 
   - Students: Can access their own data
   - Trainers: Can access student data and their own data
   - College Staff: Can access students, trainers, parents data
   - Parents: Can access their own data
   - Admins: Full access to everything

3. **Approval Requirement**: 
   - All data access requires approved status (except pending registrations)
   - Unapproved users cannot access any application data

4. **Audit Trail**: 
   - Audit logs for admin actions
   - No deletion of sensitive records

5. **Domain Verification**: 
   - Approved domains list for email validation
   - All users can read for client-side validation

6. **Notifications**: 
   - Users can manage their own notifications
   - System can create notifications via Cloud Functions

7. **Security by Default**: 
   - Default deny rule for any unlisted collections
   - Explicit permissions required for all access
*/
