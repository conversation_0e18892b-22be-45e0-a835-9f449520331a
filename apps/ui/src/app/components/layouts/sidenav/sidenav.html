<div class="card rounded-none border-t-0 border-b-0">
  <div class="mt-5">
    <ul class="menu text-base-content w-full sidenav-menu">
      @for (section of menuSections; track section) {
        @if (section.title) {
          <li class="menu-title mt-4">{{ section.title }}</li>
        }
        @for (item of section.items; track item.route) {
          <li>
            <button [routerLink]="item.route" routerLinkActive="menu-active">
              <span [class]="item.icon"></span> {{ item.label }}
            </button>
          </li>
        }
      }
    </ul>
  </div>
</div>
