import { Component } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';

interface MenuItem {
  label: string;
  route: string;
  icon: string;
}

interface MenuSection {
  title?: string;
  items: MenuItem[];
}

@Component({
  selector: 'app-sidenav',
  imports: [RouterLinkActive, RouterLink],
  templateUrl: './sidenav.html',
  styleUrls: ['./sidenav.css'],
})
export class Sidenav {
  menuSections: MenuSection[] = [
    {
      items: [
        {
          label: 'Dashboard',
          route: '/dashboard',
          icon: 'icon-[solar--home-outline]',
        },
      ],
    },
    {
      title: 'REGISTRATION',
      items: [
        {
          label: 'Institution Registration',
          route: '/registrations/institute',
          icon: 'icon-[material-symbols-light--school-outline]',
        },
        {
          label: 'Student Registration',
          route: '/registrations/student',
          icon: 'icon-[ph--student-thin]',
        },
        {
          label: 'Trainer Registration',
          route: '/registrations/trainer',
          icon: 'icon-[ph--student-thin]',
        },
        {
          label: 'Parent Registration',
          route: '/registrations/parent',
          icon: 'icon-[ph--student-thin]',
        },
        {
          label: 'Employee Registration',
          route: '/registrations/employee',
          icon: 'icon-[proicons--person]',
        },
      ],
    },
    {
      title: 'MANAGEMENT',
      items: [
        {
          label: 'Attendance',
          route: '/management/attendance',
          icon: 'icon-[garden--notes-stroke-12]',
        },
        {
          label: 'Monitor Student Data',
          route: '/management/monitor-student-data',
          icon: 'icon-[solar--notes-line-duotone]',
        },
        {
          label: 'Track Recordings',
          route: '/management/track-recordings',
          icon: 'icon-[uil--video]',
        },
        {
          label: 'Gallery',
          route: '/management/gallery',
          icon: 'icon-[solar--gallery-outline]',
        },
        {
          label: 'Employee Management',
          route: '/management/employee',
          icon: 'icon-[material-symbols--person-book-outline-sharp]',
        },
      ],
    },
    {
      title: 'SYSTEM',
      items: [
        {
          label: 'Job Posting',
          route: '/system/job-posting',
          icon: 'icon-[hugeicons--briefcase-04]',
        },
        {
          label: 'Activity Log',
          route: '/system/activity-log',
          icon: 'icon-[icon-park-outline--log]',
        },
        {
          label: 'Settings',
          route: '/system/settings',
          icon: 'icon-[iconamoon--settings-thin]',
        },
      ],
    },
  ];
}
