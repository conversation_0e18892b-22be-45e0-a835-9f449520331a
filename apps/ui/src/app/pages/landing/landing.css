/* Landing Page Animations and Styles */

/* Carousel improvements */
.carousel {
  display: flex;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
}

.carousel-item {
  scroll-snap-align: start;
  min-width: 100%;
}

/* Fade in up animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Staggered animation for service cards */
.service-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.service-card.animate {
  opacity: 1;
  transform: translateY(0);
}

.service-card:nth-child(1) {
  animation-delay: 0.1s;
}

.service-card:nth-child(2) {
  animation-delay: 0.2s;
}

.service-card:nth-child(3) {
  animation-delay: 0.3s;
}

.service-card:nth-child(4) {
  animation-delay: 0.4s;
}

.service-card:nth-child(5) {
  animation-delay: 0.5s;
}

.service-card:nth-child(6) {
  animation-delay: 0.6s;
}

/* Hero section enhancements */
.hero-content {
  animation: fadeInUp 1.2s ease-out;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom gradient backgrounds */
.gradient-bg-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Enhanced card hover effects */
.enhanced-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.enhanced-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Testimonial card animations */
.testimonial-card {
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* FAQ accordion enhancements */
.faq-item {
  transition: all 0.3s ease;
}

.faq-item:hover {
  transform: translateX(5px);
}

/* Contact form enhancements */
.contact-form {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

/* Loading animation for form submission */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Scroll to top button animation */
.scroll-to-top {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-to-top:hover {
  transform: translateY(-2px) scale(1.1);
}

/* Navigation enhancements */
.navbar {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
}

.navbar.scrolled {
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* Stats section enhancements */
.stats {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Floating navigation enhancements */
.floating-nav {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.floating-nav button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Simplified header styling */
.simplified-header {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), transparent);
  backdrop-filter: blur(10px);
}

.simplified-header .logo {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Contact form improvements */
.form-control {
  margin-bottom: 0;
}

.form-control .label {
  justify-content: flex-start;
  padding-left: 0;
  padding-right: 0;
}

.form-control .input,
.form-control .textarea {
  width: 100%;
}

/* Header scroll transitions */
.header-scrolled {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Service icon animations */
.service-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 3rem !important;
  }

  .hero p {
    font-size: 1.1rem !important;
  }

  .service-card {
    margin-bottom: 1rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Intersection Observer animations */
.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  visibility: hidden;
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
  will-change: opacity, visibility, transform;
}

.fade-in-section.is-visible {
  opacity: 1;
  transform: none;
  visibility: visible;
}

/* Enhanced button styles */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Carousel enhancements */
.carousel-item {
  transition: all 0.5s ease-in-out;
}

/* Form validation styles */
.input-error, .textarea-error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 1px #ef4444 !important;
}

.input-success, .textarea-success {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 1px #10b981 !important;
}

/* Loading states */
.btn.loading {
  pointer-events: none;
}

.btn.loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}
