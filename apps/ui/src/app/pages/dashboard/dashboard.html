@if (isLoading()) {
  <div class="flex justify-center items-center min-h-[400px]">
    <div class="loading loading-spinner loading-lg"></div>
    <span class="ml-4 text-lg">Loading dashboard...</span>
  </div>
} @else {
  @switch (userRole()) {
    @case ('admin') {
      <app-admin-dashboard />
    }
    @case ('employee') {
      <app-employee-dashboard />
    }
    @case ('parent') {
      <app-parent-dashboard />
    }
    @case ('student') {
      <app-student-dashboard />
    }
    @case ('trainer') {
      <app-trainers-dashboard />
    }
    @default {
      <div class="alert alert-warning">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <div>
          <h3 class="font-bold">Access Denied</h3>
          <div class="text-xs">You don't have permission to access any dashboard. Please contact your administrator.
          </div>
        </div>
      </div>
    }
  }
}
