import { Component, inject, OnInit, signal } from '@angular/core';
import { Auth } from '@angular/fire/auth';
import { AdminDashboard } from '../../components/dashboards/admin-dashboard/admin-dashboard';
import { EmployeeDashboard } from '../../components/dashboards/employee-dashboard/employee-dashboard';
import { ParentDashboard } from '../../components/dashboards/parent-dashboard/parent-dashboard';
import { StudentDashboard } from '../../components/dashboards/student-dashboard/student-dashboard';
import { TrainersDashboard } from '../../components/dashboards/trainers-dashboard/trainers-dashboard';

@Component({
  selector: 'app-dashboard',
  imports: [
    AdminDashboard,
    EmployeeDashboard,
    ParentDashboard,
    StudentDashboard,
    TrainersDashboard,
  ],
  templateUrl: './dashboard.html',
})
export class Dashboard implements OnInit {
  userRole = signal<string | null>(null);
  isLoading = signal(true);
  private auth: Auth = inject(Auth);

  async ngOnInit() {
    await this.getUserRole();
  }

  private async getUserRole() {
    try {
      const user = this.auth.currentUser;
      if (user) {
        const tokenResult = await user.getIdTokenResult();
        const role = tokenResult.claims?.['role'] as string;
        this.userRole.set(role);
        console.log('User role:', role);
      } else {
        console.warn('No user logged in');
        this.userRole.set(null);
      }
    } catch (error) {
      console.error('Error getting user role:', error);
      this.userRole.set(null);
    } finally {
      this.isLoading.set(false);
    }
  }
}
