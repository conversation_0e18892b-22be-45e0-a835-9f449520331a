<div class="flex justify-center items-start pt-10 h-screen bg-base-300">
  <div class="card w-96 bg-base-100 shadow-xl">
    <div class="card-body">
      <h2 class="card-title justify-center">Login</h2>
      <form (ngSubmit)="login()" [formGroup]="loginForm">
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Email</span>
          </legend>
          <input class="input input-bordered" formControlName="email" placeholder="email" type="email" />
          @if (loginForm.get('email')?.invalid && loginForm.get('email')?.touched) {
            <div class="text-error text-sm mt-1">Email is required.</div>
          }
        </fieldset>
        <fieldset class="form-control mt-4">
          <legend class="label">
            <span class="label-text">Password</span>
          </legend>
          <input class="input input-bordered" formControlName="password" placeholder="password" type="password" />
          @if (loginForm.get('password')?.invalid && loginForm.get('password')?.touched) {
            <div class="text-error text-sm mt-1">Password is required.</div>
          }
        </fieldset>
        <div class="flex justify-between items-center mt-4">
          <div class="form-control">
            <label class="label cursor-pointer">
              <input class="checkbox checkbox-primary" type="checkbox" />
              <span class="label-text ml-2">Remember me</span>
            </label>
          </div>
          <div>
            <a class="label-text-alt link link-hover" routerLink="/auth/forgot-password">Forgot password?</a>
          </div>
        </div>
        <div class="form-control mt-6">
          <button class="btn btn-primary btn-block" type="submit">Login</button>
        </div>
      </form>
      <div class="divider">New to Worthy Freshers?</div>
      <div class="flex justify-between">
        <button (click)="loginWithGoogle()" class="btn bg-white text-black border-[#e5e5e5]">
          <svg aria-label="Google logo" height="16" viewBox="0 0 512 512" width="16" xmlns="http://www.w3.org/2000/svg">
            <g>
              <path d="m0 0H512V512H0" fill="#fff"></path>
              <path d="M153 292c30 82 118 95 171 60h62v48A192 192 0 0190 341" fill="#34a853"></path>
              <path d="m386 400a140 175 0 0053-179H260v74h102q-7 37-38 57" fill="#4285f4"></path>
              <path d="m90 341a208 200 0 010-171l63 49q-12 37 0 73" fill="#fbbc02"></path>
              <path d="m153 219c22-69 116-109 179-50l55-54c-78-75-230-72-297 55" fill="#ea4335"></path>
            </g>
          </svg>
        </button>
        <a class="btn btn-outline btn-primary" routerLink="/auth/register">Create New Account</a>
      </div>
    </div>
  </div>
</div>
