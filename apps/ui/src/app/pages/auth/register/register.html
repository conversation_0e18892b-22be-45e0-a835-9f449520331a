<div class="flex justify-center items-start pt-10 h-screen bg-base-300">
  <div class="card w-96 bg-base-100 shadow-xl">
    <div class="card-body">
      <h2 class="card-title justify-center">Register</h2>
      <form (ngSubmit)="register()" [formGroup]="registerForm">
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Name</span>
          </legend>
          <input class="input input-bordered" formControlName="name" placeholder="Firstname, Lastname" type="text" />
          @if (registerForm.get('name')?.invalid && registerForm.get('name')?.touched) {
            <div class="text-error text-sm mt-1">Name is required.</div>
          }
        </fieldset>
        <fieldset class="form-control mt-4">
          <legend class="label">
            <span class="label-text">Email</span>
          </legend>
          <input class="input input-bordered" formControlName="email" placeholder="email" type="email" />
          @if (registerForm.get('email')?.invalid && registerForm.get('email')?.touched) {
            <div class="text-error text-sm mt-1">Email is required.</div>
          }
        </fieldset>
        <fieldset class="form-control mt-4">
          <legend class="label">
            <span class="label-text">Password</span>
          </legend>
          <input class="input input-bordered" formControlName="password" placeholder="password" type="password" />
          @if (registerForm.get('password')?.invalid && registerForm.get('password')?.touched) {
            <div class="text-error text-sm mt-1">Password is required.</div>
          }
        </fieldset>
        <fieldset class="form-control mt-4">
          <legend class="label">
            <span class="label-text">Role</span>
          </legend>
          <select class="select select-bordered" formControlName="role">
            <option disabled value="">Select your role</option>
            <option>student</option>
            <option>trainer</option>
            <option>parent</option>
            <option>college-staff</option>
            <option>admin</option>
          </select>
          @if (registerForm.get('role')?.invalid && registerForm.get('role')?.touched) {
            <div class="text-error text-sm mt-1">Role is required.</div>
          }
        </fieldset>
        <div class="form-control mt-6">
          <button
            class="btn btn-primary btn-block"
            type="submit"
            [class.loading]="isRegistering"
            [disabled]="isRegistering || !registerForm.valid">
            @if (!isRegistering) {
              Register
            } @else {
              Creating Account...
            }
          </button>
        </div>

        <!-- Registration Info Alert -->
        <div class="alert alert-info mt-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h3 class="font-bold">Registration Process</h3>
            <div class="text-xs">
              Your registration will be reviewed by our administrators before you can access the platform.
              You will receive an email notification once your account is approved.
            </div>
          </div>
        </div>
      </form>
      <div class="divider">Already have an account?</div>
      <a class="btn btn-outline btn-primary" routerLink="/auth/login">Login</a>
    </div>
  </div>
</div>
