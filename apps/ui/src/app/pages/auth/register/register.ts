import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Auth,
  createUserWithEmailAndPassword,
  updateProfile,
} from '@angular/fire/auth';
import { Router, RouterLink } from '@angular/router';
import { toastConfig$ } from '../../../store/signals';
import { PendingRegistrationService } from '../../../services/pending-registration.service';

@Component({
  selector: 'app-register',
  imports: [ReactiveFormsModule, CommonModule, RouterLink],
  templateUrl: './register.html',
})
export class Register {
  private auth: Auth = inject(Auth);
  private router: Router = inject(Router);
  private pendingRegistrationService: PendingRegistrationService = inject(PendingRegistrationService);

  // Form for user registration with validation
  registerForm = new FormGroup({
    name: new FormControl('', [Validators.required, Validators.minLength(2)]),
    email: new FormControl('', [Validators.required, Validators.email]),
    password: new FormControl('', [Validators.required, Validators.minLength(6)]),
    role: new FormControl('', [Validators.required]),
  });

  // Loading state to prevent multiple submissions
  isRegistering = false;

  /**
   * Handles user registration with the new pending approval system
   * Creates Firebase Auth user and submits registration for admin approval
   */
  async register() {
    if (this.registerForm.valid && !this.isRegistering) {
      this.isRegistering = true;
      const data: any = this.registerForm.value;

      console.log('Starting registration process for:', data.email);

      try {
        // Step 1: Create Firebase Auth user
        const userCredential = await createUserWithEmailAndPassword(
          this.auth,
          data.email,
          data.password
        );

        console.log('Firebase Auth user created:', userCredential.user.uid);

        // Step 2: Update user profile with display name
        await updateProfile(userCredential.user, { displayName: data.name });

        // Step 3: Create pending registration (replaces immediate role assignment)
        const registrationResult = await this.pendingRegistrationService.createPendingRegistration({
          uid: userCredential.user.uid,
          email: data.email,
          name: data.name,
          role: data.role,
          verificationData: this.getVerificationData(data.role)
        });

        console.log('Pending registration created:', registrationResult);

        // Show success message and redirect to pending approval page
        toastConfig$.set({
          status: 'success',
          message: 'Registration submitted successfully! Please wait for admin approval.'
        });

        // Redirect to a pending approval page instead of dashboard
        await this.router.navigate(['/auth/pending-approval']);

      } catch (e: any) {
        console.error('Error during registration:', e);

        // Handle specific Firebase Auth errors
        let errorMessage = 'Registration failed. Please try again.';

        if (e.code === 'auth/email-already-in-use') {
          errorMessage = 'An account with this email already exists.';
        } else if (e.code === 'auth/weak-password') {
          errorMessage = 'Password is too weak. Please use at least 6 characters.';
        } else if (e.code === 'auth/invalid-email') {
          errorMessage = 'Please enter a valid email address.';
        } else if (e.message) {
          errorMessage = e.message;
        }

        toastConfig$.set({ status: 'error', message: errorMessage });
      } finally {
        this.isRegistering = false;
      }
    } else if (!this.registerForm.valid) {
      console.error('Registration form is invalid');
      toastConfig$.set({ status: 'error', message: 'Please fill in all required fields correctly.' });
    }
  }

  /**
   * Generates role-specific verification data
   * This can be expanded to include additional verification requirements
   *
   * @param role - The selected user role
   * @returns Verification data object
   */
  private getVerificationData(role: string): any {
    const verificationData: any = {};

    switch (role) {
      case 'student':
        // Students might need to provide student ID in the future
        verificationData.requiresStudentId = true;
        break;

      case 'trainer':
        // Trainers need credential verification
        verificationData.requiresCredentials = true;
        verificationData.requiresExperienceVerification = true;
        break;

      case 'college-staff':
        // College staff need institutional email verification
        verificationData.requiresInstitutionalEmail = true;
        verificationData.requiresDepartmentVerification = true;
        break;

      case 'parent':
        // Parents need to be linked to a student
        verificationData.requiresStudentLinkage = true;
        break;
    }

    return verificationData;
  }
}
