import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { Auth } from '@angular/fire/auth';
import { PendingRegistrationService, RegistrationStatus } from '../../../services/pending-registration.service';
import { toastConfig$ } from '../../../store/signals';

@Component({
  selector: 'app-pending-approval',
  imports: [CommonModule, RouterLink],
  templateUrl: './pending-approval.html'
})
export class PendingApproval implements OnInit {
  private auth: Auth = inject(Auth);
  private router: Router = inject(Router);
  private pendingRegistrationService: PendingRegistrationService = inject(PendingRegistrationService);

  // Reactive signals for component state
  registrationStatus = signal<RegistrationStatus | null>(null);
  isLoading = signal(true);
  isRefreshing = signal(false);

  async ngOnInit() {
    await this.checkRegistrationStatus();
    
    // Set up periodic status checking (every 30 seconds)
    setInterval(() => {
      this.refreshStatus();
    }, 30000);
  }

  /**
   * Checks the current user's registration status
   * Redirects approved users to dashboard, shows status for others
   */
  async checkRegistrationStatus() {
    try {
      this.isLoading.set(true);
      
      // Ensure user is authenticated
      const user = this.auth.currentUser;
      if (!user) {
        console.warn('No authenticated user found, redirecting to login');
        await this.router.navigate(['/auth/login']);
        return;
      }

      // Get registration status from service
      const status = await this.pendingRegistrationService.getCurrentUserRegistrationStatus();
      this.registrationStatus.set(status);

      console.log('Registration status:', status);

      // Handle different status scenarios
      if (status.status === 'approved') {
        // User is approved, redirect to dashboard
        toastConfig$.set({ 
          status: 'success', 
          message: 'Your registration has been approved! Welcome!' 
        });
        await this.router.navigate(['/dashboard']);
      } else if (status.status === 'rejected') {
        // User is rejected, show rejection reason
        toastConfig$.set({ 
          status: 'error', 
          message: `Registration rejected: ${status.registrationData?.rejectionReason || 'No reason provided'}` 
        });
      } else if (status.status === 'not_found') {
        // No registration found, redirect to register
        console.warn('No registration found for user, redirecting to register');
        await this.router.navigate(['/auth/register']);
      }
      // If status is 'pending', stay on this page

    } catch (error) {
      console.error('Error checking registration status:', error);
      toastConfig$.set({ 
        status: 'error', 
        message: 'Unable to check registration status. Please try again.' 
      });
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Refreshes the registration status without showing loading spinner
   * Used for periodic updates
   */
  async refreshStatus() {
    if (this.isRefreshing()) return; // Prevent multiple simultaneous refreshes

    try {
      this.isRefreshing.set(true);
      await this.checkRegistrationStatus();
    } catch (error) {
      console.error('Error refreshing status:', error);
    } finally {
      this.isRefreshing.set(false);
    }
  }

  /**
   * Manual refresh triggered by user
   */
  async onRefreshClick() {
    await this.refreshStatus();
    toastConfig$.set({ 
      status: 'info', 
      message: 'Status refreshed' 
    });
  }

  /**
   * Logs out the current user
   */
  async logout() {
    try {
      await this.auth.signOut();
      await this.router.navigate(['/']);
    } catch (error) {
      console.error('Error logging out:', error);
      toastConfig$.set({ 
        status: 'error', 
        message: 'Error logging out. Please try again.' 
      });
    }
  }

  /**
   * Gets a user-friendly status message
   */
  getStatusMessage(): string {
    const status = this.registrationStatus();
    if (!status) return '';

    switch (status.status) {
      case 'pending':
        return 'Your registration is being reviewed by our administrators. You will receive access once approved.';
      case 'rejected':
        return `Your registration was rejected. Reason: ${status.registrationData?.rejectionReason || 'No reason provided'}`;
      case 'approved':
        return 'Your registration has been approved! Redirecting to dashboard...';
      case 'not_found':
        return 'No registration found. Please register first.';
      default:
        return 'Unknown status. Please contact support.';
    }
  }

  /**
   * Gets the appropriate CSS class for status styling
   */
  getStatusClass(): string {
    const status = this.registrationStatus();
    if (!status) return '';

    switch (status.status) {
      case 'pending':
        return 'alert-warning';
      case 'rejected':
        return 'alert-error';
      case 'approved':
        return 'alert-success';
      default:
        return 'alert-info';
    }
  }

  /**
   * Formats timestamp for display
   */
  formatDate(timestamp: any): string {
    if (!timestamp) return 'Unknown';
    
    try {
      // Handle Firestore Timestamp
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  }

  /**
   * Gets role display name
   */
  getRoleDisplayName(role: string): string {
    switch (role) {
      case 'student':
        return 'Student';
      case 'trainer':
        return 'Trainer';
      case 'parent':
        return 'Parent';
      case 'college-staff':
        return 'College Staff';
      default:
        return role;
    }
  }
}
