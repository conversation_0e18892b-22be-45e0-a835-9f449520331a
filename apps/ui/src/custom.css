
main {
  padding-top: 4rem;
  min-height: calc(100vh - 3.25rem);
}

.card-header {
  /*border-bottom: 1px solid var(--color-neutral);*/
  padding: 1rem 2rem 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.card-footer {
  border-top: 1px solid var(--color-neutral);
  padding: 2rem;
  display: flex;
  flex-direction: column;
}

/* Hide arrows in input[type=number] for Chrome, Safari, Edge, and Opera */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Hide arrows in input[type=number] for Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.select, .select-sm {
  line-height: 1rem;
}

input[type="datetime-local"]:focus {
  outline: none !important;
}

input[type="search"]:focus {
  outline: none !important;
}

input:focus, select:focus {
  outline: none;
}

.max-h-75vh {
  max-height: 75vh;
}

.max-h-75vh {
  max-height: 75vh;
}

/*input[type="file"]::file-selector-button {*/
/*  background-color: transparent;*/
/*}*/

/*Override DaisyUI button styles to ensure box shape across all themes*/
li, button, .btn {
  border-radius: 0.25rem !important;
}

.btn-sm, .btn-xs, .btn-lg {
  border-radius: 0 !important;
}

.dropdown-content {
  border-radius: 0 !important;
}

select, .select, .select-sm {
  border-radius: 0 !important;
}

.input, .input-sm, .textarea {
  border-radius: 0 !important;
}

input[type="file"]::file-selector-button {
  background-color: transparent;
  border: none;
}

.menu, .menu-item {
  border-radius: 0 !important;
}

.card, .card-body {
  border-radius: 0 !important;
}

.badge {
  border-radius: 0.25rem !important;
}

app-footer {
  padding-top: 2rem;
}
