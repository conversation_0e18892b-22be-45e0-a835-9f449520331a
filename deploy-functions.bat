@echo off
echo Deploying Firebase Functions...
echo.

echo Building functions...
call npm run build:functions
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b %errorlevel%
)

echo.
echo Deploying functions...
call npm run deploy:functions
if %errorlevel% neq 0 (
    echo Deploy failed!
    pause
    exit /b %errorlevel%
)

echo.
echo ✅ Functions deployed successfully!
echo.
echo New HTTP endpoints available:
echo - getRegistrationStatusHttp
echo - approvePendingRegistrationHttp  
echo - rejectPendingRegistrationHttp
echo.
echo The Angular app will now use HTTP as primary method with onCall fallback.
echo.
pause
