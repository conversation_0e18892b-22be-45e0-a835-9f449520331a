# CORS Troubleshooting Guide for Firebase Functions

## 🔍 Understanding the CORS Error

CORS (Cross-Origin Resource Sharing) errors occur when your Angular app (running on one domain) tries to call Firebase Cloud Functions (running on another domain). This is a browser security feature.

## ✅ Solutions Implemented

### 1. **Dual Function Approach**
We've implemented two versions of the `createPendingRegistration` function:

- **Primary**: `createPendingRegistration` (onCall function) - Preferred method
- **Fallback**: `createPendingRegistrationHttp` (HTTP function) - CORS-enabled backup

### 2. **Automatic Fallback Logic**
The Angular service automatically tries the onCall function first, then falls back to HTTP if CORS issues occur:

```typescript
// Service automatically handles fallback
await this.pendingRegistrationService.createPendingRegistration(userData);
```

## 🚀 Deployment Steps

### Step 1: Deploy the Updated Functions
```bash
cd functions/firebase
npm install
firebase deploy --only functions
```

### Step 2: Update Your Project ID
In `apps/ui/src/app/services/pending-registration.service.ts`, update:
```typescript
private readonly FIREBASE_PROJECT_ID = 'your-actual-project-id'; // Replace this!
```

### Step 3: Test Both Methods
After deployment, test the registration flow. Check browser console for logs:
- ✅ "Pending registration created via onCall" = Primary method working
- ⚠️ "onCall function failed, trying HTTP fallback" = Using backup method

## 🔧 Configuration Options

### Option 1: Use onCall Functions (Recommended)
Firebase onCall functions handle CORS automatically and are more secure:
```typescript
// This should work without CORS issues
const createPendingRegistration = httpsCallable(this.functions, 'createPendingRegistration');
```

### Option 2: Use HTTP Functions (Fallback)
If onCall functions have issues, the HTTP version provides explicit CORS control:
```typescript
// Fallback with manual CORS handling
const url = `https://${region}-${projectId}.cloudfunctions.net/createPendingRegistrationHttp`;
```

## 🐛 Common Issues & Solutions

### Issue 1: "Access to fetch blocked by CORS policy"
**Cause**: Browser blocking cross-origin request
**Solution**: 
1. Ensure functions are deployed: `firebase deploy --only functions`
2. Check project ID is correct in service
3. Verify function region matches (default: us-central1)

### Issue 2: "Function not found"
**Cause**: Function not deployed or wrong name
**Solution**:
```bash
# Check deployed functions
firebase functions:list

# Redeploy if missing
firebase deploy --only functions
```

### Issue 3: "Authentication required"
**Cause**: User not authenticated when calling function
**Solution**: Ensure user is logged in before calling:
```typescript
const user = this.auth.currentUser;
if (!user) {
  throw new Error('User must be authenticated');
}
```

## 🔍 Debugging Steps

### 1. Check Browser Console
Look for these log messages:
- ✅ "Attempting to create pending registration via onCall function..."
- ⚠️ "onCall function failed, trying HTTP fallback:"
- ❌ "Both onCall and HTTP methods failed:"

### 2. Check Firebase Functions Logs
```bash
firebase functions:log --only createPendingRegistration
firebase functions:log --only createPendingRegistrationHttp
```

### 3. Test Function Directly
Test the HTTP function with curl:
```bash
curl -X POST \
  https://us-central1-your-project.cloudfunctions.net/createPendingRegistrationHttp \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ID_TOKEN" \
  -d '{"uid":"test","email":"<EMAIL>","name":"Test","role":"student"}'
```

### 4. Verify Project Configuration
Check these files have correct project ID:
- `apps/ui/src/environments/environment.ts`
- `firebase.json`
- `.firebaserc`

## 🛠️ Manual CORS Fix (If Needed)

If you still have CORS issues, you can manually configure CORS for your domain:

### 1. Update HTTP Function CORS Headers
In `functions/firebase/src/lib/pending-registration.ts`:
```typescript
// Replace '*' with your specific domain for production
res.set('Access-Control-Allow-Origin', 'https://your-domain.com');
```

### 2. Add Domain to Firebase Hosting
Ensure your domain is authorized in Firebase Console:
1. Go to Firebase Console → Authentication → Settings
2. Add your domain to "Authorized domains"

## 📋 Testing Checklist

- [ ] Functions deployed successfully
- [ ] Project ID updated in service
- [ ] User can authenticate
- [ ] Registration form submits without errors
- [ ] Pending registration appears in Firestore
- [ ] Admin can see pending registration in dashboard
- [ ] No CORS errors in browser console

## 🚨 Emergency Workaround

If all else fails, you can temporarily disable CORS in your browser for testing:

**Chrome (Development Only)**:
```bash
chrome --disable-web-security --user-data-dir=/tmp/chrome_dev_session
```

**⚠️ WARNING**: Never use this in production or with real user data!

## 📞 Getting Help

If you're still experiencing CORS issues:

1. **Check Firebase Status**: https://status.firebase.google.com/
2. **Verify Function Deployment**: `firebase functions:list`
3. **Check Browser Network Tab**: Look for failed requests
4. **Review Function Logs**: `firebase functions:log`
5. **Test with Different Browser**: Rule out browser-specific issues

## 🎯 Expected Behavior

After implementing these fixes:

1. **Registration Flow**:
   - User fills registration form
   - Service tries onCall function first
   - If CORS error, automatically tries HTTP function
   - Registration succeeds with either method

2. **Console Logs**:
   ```
   ✅ Attempting to create pending registration via onCall function...
   ✅ Pending registration created via onCall: {success: true, ...}
   ```
   
   OR
   
   ```
   ⚠️ onCall function failed, trying HTTP fallback: [error details]
   ✅ Creating pending registration via HTTP: https://...
   ✅ Pending registration created via HTTP: {success: true, ...}
   ```

The user experience remains the same regardless of which method succeeds - they just see "Registration submitted successfully!"
