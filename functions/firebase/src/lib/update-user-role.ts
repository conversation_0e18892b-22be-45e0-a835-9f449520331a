import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

admin.initializeApp();

/**
 * Legacy function for updating user roles
 * @deprecated Use the new pending registration system instead
 * This function is kept for backward compatibility but should not be used for new registrations
 */
export const updateUserRole = functions.https.onCall(
  async (request: any, context: any) => {
    const { uid, role } = request.data;
    console.log('Data', request.data);

    if (!uid || !role) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        "The function must be called with two arguments 'uid' and 'role'."
      );
    }

    try {
      // Set custom user claims on the user with pending status
      // This ensures no immediate access until admin approval
      await admin.auth().setCustomUserClaims(uid, {
        role,
        status: 'pending',
        registeredAt: Date.now()
      });

      return {
        message: `Success! User ${uid} has been registered with role ${role} and is pending approval.`,
      };
    } catch (error) {
      console.error(error);
      throw new functions.https.HttpsError(
        'internal',
        'Unable to update user role.'
      );
    }
  }
);
